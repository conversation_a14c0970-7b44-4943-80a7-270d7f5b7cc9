import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { CheckCircle, ArrowRight, Loader2 } from 'lucide-react';
import { UseMutationResult } from '@tanstack/react-query';
import { findNextItemInCourse } from '@/services/course/courseApi';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

export interface LessonFooterProps {
  isCompleted: boolean;
  onComplete: () => void;
  onNext: () => void;
  completionMutation: UseMutationResult<any, any, any>;
  hasNextLesson: boolean;
  progress: number;
  courseId: string;
  currentLessonSlug: string;
}

const LessonFooter: React.FC<LessonFooterProps> = ({
  isCompleted,
  onComplete,
  onNext,
  completionMutation,
  hasNextLesson,
  progress,
  courseId,
  currentLessonSlug
}) => {
  const [buttonClicked, setButtonClicked] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const { user } = useAuth();
  const navigate = useNavigate();

  // Handle completion button click with debounce
  const handleCompleteClick = () => {
    if (buttonClicked || completionMutation.isPending) return;

    // Set local state to prevent double clicks
    setButtonClicked(true);

    // Call the actual completion handler
    onComplete();

    // Reset after a delay (just in case the mutation fails)
    setTimeout(() => setButtonClicked(false), 2000);
  };

  // Handle next item click - complete lesson first, then navigate to next item (lesson or test)
  const handleNextClick = async () => {
    if (isNavigating || completionMutation.isPending || !user) return;

    setIsNavigating(true);

    try {
      // If lesson is not completed, mark it as completed first
      if (!isCompleted) {
        await completionMutation.mutateAsync();
      }

      // Find the next item in the course sequence (could be lesson or test)
      const nextItem = await findNextItemInCourse(currentLessonSlug, user.id);

      if (nextItem.nextItemType && nextItem.nextItemSlug) {
        switch (nextItem.nextItemType) {
          case 'lesson':
            navigate(`/course/${courseId}/lesson/${nextItem.nextItemSlug}`);
            break;
          case 'pre_test':
          case 'post_test':
            navigate(`/course/${courseId}/module/${nextItem.nextItemSlug}/test?type=${nextItem.nextItemType}`);
            break;
          default:
            // Fallback to original onNext behavior
            onNext();
        }
      } else {
        // No next item found, use original onNext behavior or go to modules
        if (hasNextLesson) {
          onNext();
        } else {
          navigate(`/course/${courseId}/modules`);
        }
      }
    } catch (error) {
      console.error('Error completing lesson before navigation:', error);
      // Fallback to original onNext behavior
      onNext();
    } finally {
      setIsNavigating(false);
    }
  };

  // Track if button is disabled
  const isButtonDisabled = buttonClicked || completionMutation.isPending;

  return (
    <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t">
      {/* Completion section */}
      <div className="flex items-center gap-2">
        {isCompleted ? (
          <div className="flex items-center gap-2 text-green-600 bg-green-50 px-4 py-2 rounded-md dark:bg-green-900/20 dark:text-green-400">
            <CheckCircle className="h-5 w-5" />
            <span>Completed</span>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <div className="w-24 bg-muted rounded-full h-2 overflow-hidden">
              <motion.div
                className="h-full bg-primary"
                style={{ width: `${progress}%` }}
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            <span className="text-sm text-muted-foreground">{progress}%</span>

            <Button
              onClick={handleCompleteClick}
              disabled={isButtonDisabled}
              variant="outline"
              className="ml-2 min-w-[140px]"
            >
              {isButtonDisabled ? (
                <span className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Marking...
                </span>
              ) : (
                'Mark as Complete'
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Next lesson button */}
      <Button
        onClick={handleNextClick}
        disabled={isNavigating || completionMutation.isPending}
        variant="default"
        size="lg"
        className="flex items-center gap-2"
      >
        {isNavigating ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            {isCompleted ? 'Loading...' : 'Completing...'}
          </>
        ) : (
          <>
            Next
            <ArrowRight className="h-4 w-4" />
          </>
        )}
      </Button>
    </div>
  );
};

export default LessonFooter;
