import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { UseMutationResult } from '@tanstack/react-query';

export interface LessonNavigationProps {
  courseId: string;
  previousLesson?: string;
  nextLesson?: string;
  isCompleted?: boolean;
  completionMutation?: UseMutationResult<any, any, any>;
}

const LessonNavigation: React.FC<LessonNavigationProps> = ({
  courseId,
  previousLesson,
  nextLesson,
  isCompleted = false,
  completionMutation
}) => {
  const [isNavigating, setIsNavigating] = useState(false);
  const navigate = useNavigate();

  // Handle next lesson click - complete lesson first, then navigate
  const handleNextClick = async (e: React.MouseEvent) => {
    e.preventDefault();

    if (!nextLesson || isNavigating || completionMutation?.isPending) return;

    setIsNavigating(true);

    try {
      // If lesson is not completed and we have a completion mutation, mark it as completed first
      if (!isCompleted && completionMutation) {
        await completionMutation.mutateAsync();
      }

      // Then navigate to next lesson
      navigate(`/course/${courseId}/lesson/${nextLesson}`);
    } catch (error) {
      console.error('Error completing lesson before navigation:', error);
      // Still navigate even if completion fails
      navigate(`/course/${courseId}/lesson/${nextLesson}`);
    } finally {
      setIsNavigating(false);
    }
  };
  return (
    <div className="flex justify-between items-center py-2 mb-6 border-y">
      {previousLesson ? (
        <Button
          variant="ghost"
          asChild
          className="flex items-center gap-1"
        >
          <Link to={`/course/${courseId}/lesson/${previousLesson}`}>
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Link>
        </Button>
      ) : (
        <Button variant="ghost" disabled className="invisible">
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>
      )}

      {/* Removed "Back to Modules" button */}
      <div className="flex-1"></div>

      {nextLesson ? (
        <Button
          variant="ghost"
          onClick={handleNextClick}
          disabled={isNavigating || completionMutation?.isPending}
          className="flex items-center gap-1"
        >
          {isNavigating ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              {isCompleted ? 'Loading...' : 'Completing...'}
            </>
          ) : (
            <>
              Next
              <ChevronRight className="h-4 w-4" />
            </>
          )}
        </Button>
      ) : (
        <Button variant="ghost" disabled className="invisible">
          Next
          <ChevronRight className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
};

export default LessonNavigation;
