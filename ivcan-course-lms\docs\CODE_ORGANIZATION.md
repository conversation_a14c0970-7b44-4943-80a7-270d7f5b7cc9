# Code Organization Guide

This document provides guidelines for organizing code in the IVCAN Course LMS project.

## Directory Structure

The project follows a feature-based organization with the following main directories:

```
src/
├── components/       # UI components
│   ├── admin/        # Admin-specific components
│   ├── auth/         # Authentication components
│   ├── course/       # Course-related components
│   ├── error-boundaries/ # Error boundary components
│   ├── layout/       # Layout components
│   ├── ui/           # Reusable UI components
│   └── ...
├── context/          # React context providers
├── hooks/            # Custom React hooks
├── integrations/     # Third-party integrations
│   ├── supabase/     # Supabase integration
│   └── ...
├── lib/              # Utility libraries
├── pages/            # Page components
├── services/         # Business logic services
│   ├── auth/         # Authentication services
│   ├── course/       # Course-related services
│   ├── error/        # Error handling services
│   └── ...
├── styles/           # Global styles
├── types/            # TypeScript type definitions
└── utils/            # Utility functions
```

## Component Organization

### UI Components

UI components should be organized in the `components/ui` directory. These are reusable components that don't have business logic.

Example:
```
components/ui/
├── button.tsx
├── card.tsx
├── dialog.tsx
└── ...
```

### Feature Components

Feature-specific components should be organized in feature-specific directories.

Example:
```
components/course/
├── CourseCard.tsx
├── CourseList.tsx
├── LessonPlayer.tsx
└── ...
```

### Page Components

Page components should be organized in the `pages` directory. These are top-level components that represent a page in the application.

Example:
```
pages/
├── Admin.tsx
├── Course.tsx
├── Dashboard.tsx
└── ...
```

## Service Organization

Services should be organized by feature in the `services` directory.

Example:
```
services/
├── auth/
│   ├── authService.ts
│   └── roleService.ts
├── course/
│   ├── courseService.ts
│   └── lessonService.ts
└── ...
```

## Naming Conventions

### Files

- React components: PascalCase (e.g., `CourseCard.tsx`)
- Hooks: camelCase with `use` prefix (e.g., `useAuth.ts`)
- Services: camelCase with `Service` suffix (e.g., `authService.ts`)
- Utilities: camelCase (e.g., `formatDate.ts`)

### Directories

- Feature directories: lowercase (e.g., `course`, `auth`)
- Component directories: lowercase (e.g., `ui`, `layout`)

## Import Organization

Imports should be organized in the following order:

1. React and related libraries
2. Third-party libraries
3. Project imports (absolute paths)
4. Local imports (relative paths)

Example:
```typescript
// React and related libraries
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// Third-party libraries
import { toast } from 'sonner';
import { motion } from 'framer-motion';

// Project imports (absolute paths)
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';

// Local imports (relative paths)
import { CourseCard } from './CourseCard';
import { useCourseData } from './hooks/useCourseData';
```

## Code Organization Tool

The project includes a code organization tool to help identify potential issues:

```bash
# Run all checks
npm run organize:code

# Check for unused files
npm run organize:check-unused

# Check for duplicate code
npm run organize:check-duplicates
```

## Best Practices

1. **Keep components focused**: Each component should have a single responsibility
2. **Use hooks for reusable logic**: Extract reusable logic into custom hooks
3. **Separate business logic from UI**: Use services for business logic
4. **Group related files**: Keep related files together in feature directories
5. **Use index files for exports**: Use index files to simplify imports
6. **Avoid deep nesting**: Keep the directory structure flat when possible
7. **Be consistent**: Follow the established patterns in the codebase
