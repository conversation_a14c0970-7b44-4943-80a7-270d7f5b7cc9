import { supabase } from '@/integrations/supabase/client';
import { updateModuleCompletion, updateCourseProgress } from './courseApi';
import { UserLessonProgress, UserCourseProgress } from './types';
import { toast } from 'sonner';
import { executeWithRetry } from '@/lib/connection-manager';
import { awardModuleBadge, awardCourseBadge } from '@/services/achievements/achievementService';

// Mark a lesson as completed
export const completeLessonProgress = async (lessonId: string, userId: string): Promise<boolean> => {
  try {
    console.log(`Starting completeLessonProgress for lesson ${lessonId} and user ${userId}`);

    // Get the module ID for this lesson
    const { data: lesson, error: lessonError } = await supabase
      .from('lessons')
      .select('module_id')
      .eq('id', lessonId)
      .single();

    if (lessonError || !lesson) {
      console.error('Error getting lesson:', lessonError);
      return false;
    }

    // First, check if a progress record already exists
    const { data: existingProgress, error: checkError } = await supabase
      .from('user_lesson_progress')
      .select('id, is_completed')
      .eq('user_id', userId)
      .eq('lesson_id', lessonId)
      .maybeSingle();

    const now = new Date().toISOString();
    const progressRecord: Record<string, any> = {};
    
    if (existingProgress) {
      console.log('Found existing progress record');
      
      // Use is_completed for consistency
      progressRecord.is_completed = true;
      progressRecord.completed_at = now;
      
      // Update the existing record
      const { error: updateError } = await supabase
        .from('user_lesson_progress')
        .update({
          ...progressRecord,
          updated_at: now
        })
        .eq('id', existingProgress.id);

      if (updateError) {
        console.error('Error updating lesson progress:', updateError);
        return false;
      }
    } else {
      console.log('Creating new progress record');
      
      // Create a new record with is_completed for consistency
      const { error: insertError } = await supabase
        .from('user_lesson_progress')
        .insert({
          user_id: userId,
          lesson_id: lessonId,
          is_completed: true,
          completed_at: now,
          progress_percent: 100,
          created_at: now,
          updated_at: now
        });

      if (insertError) {
        console.error('Error creating lesson progress:', insertError);
        return false;
      }
    }

    // Check and update module completion
    await checkAndUpdateModuleCompletion(lesson.module_id, userId);
    
    return true;
  } catch (error) {
    console.error('Unexpected error in completeLessonProgress:', error);
    return false;
  }
};

// Helper function to check and update module completion
const checkAndUpdateModuleCompletion = async (moduleId: string, userId: string): Promise<void> => {
  try {
    const isModuleCompleted = await checkModuleCompletion(moduleId, userId);
    
    if (isModuleCompleted) {
      console.log(`Module ${moduleId} is now completed by user ${userId}`);
      await updateModuleCompletion(moduleId, userId, true);
      
      // Get module number/index to pass to badge award function
      try {
        const { data: moduleData } = await supabase
          .from('modules')
          .select('module_number, course_id')
          .eq('id', moduleId)
          .single();
        
        // Award a badge for completing the module (use module_number - 1 as index)
        if (moduleData) {
          const moduleIndex = (moduleData.module_number || 1) - 1;
          await awardModuleBadge(userId, moduleId, moduleIndex);
          
          // Check if all modules in the course are completed
          const isCourseCompleted = await checkCourseCompletion(moduleData.course_id, userId);
          
          if (isCourseCompleted) {
            console.log(`Course ${moduleData.course_id} is now completed by user ${userId}`);
            
            // Award a badge for completing the course
            await awardCourseBadge(userId, moduleData.course_id);
          }
        }
      } catch (e) {
        console.error('Error getting module data for badge award:', e);
        // Still try to award badge with default index 0
        await awardModuleBadge(userId, moduleId, 0);
      }
    }
  } catch (error) {
    console.error('Error in checkAndUpdateModuleCompletion:', error);
  }
};

// Check if all modules in a course are completed
const checkCourseCompletion = async (courseId: string, userId: string): Promise<boolean> => {
  try {
    // Get all modules for this course
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id')
      .eq('course_id', courseId);

    if (modulesError || !modules) {
      console.error('Error fetching modules:', modulesError);
      return false;
    }

    if (modules.length === 0) {
      console.log(`No modules found for course ${courseId}`);
      return false;
    }

    // Get completed modules for this user
    const { data: userModuleProgress, error: progressError } = await supabase
      .from('user_module_progress')
      .select('module_id')
      .eq('user_id', userId)
      .eq('is_completed', true)
      .in('module_id', modules.map(m => m.id));

    if (progressError) {
      console.error('Error fetching completed modules:', progressError);
      return false;
    }

    // Check if all modules are completed
    const allModulesCompleted = userModuleProgress?.length === modules.length;
    
    return allModulesCompleted;
  } catch (error) {
    console.error('Error in checkCourseCompletion:', error);
    return false;
  }
};

// Check if all lessons in a module are completed
export const checkModuleCompletion = async (moduleId: string, userId: string): Promise<boolean> => {
  try {
    console.log(`Checking module completion for module ${moduleId} and user ${userId}`);

    // Get all lessons for this module
    const { data: lessons, error: lessonsError } = await supabase
      .from('lessons')
      .select('id')
      .eq('module_id', moduleId);

    if (lessonsError || !lessons) {
      console.error('Error fetching lessons:', lessonsError);
      return false;
    }

    if (lessons.length === 0) {
      console.log('No lessons found for module');
      return false;
    }

    // Get completed lessons for this module
    const { data: completedLessons, error: progressError } = await supabase
      .from('user_lesson_progress')
      .select('lesson_id')
      .eq('user_id', userId)
      .eq('is_completed', true)
      .in('lesson_id', lessons.map(l => l.id));

    if (progressError) {
      console.error('Error fetching completed lessons:', progressError);
      return false;
    }

    const allLessonsCompleted = completedLessons && completedLessons.length === lessons.length;
    console.log(`Module has ${completedLessons?.length || 0} completed lessons out of ${lessons.length}`);

    return allLessonsCompleted;
  } catch (error) {
    console.error('Error in checkModuleCompletion:', error);
    return false;
  }
};

// Check if all modules in a course are completed and update course completion status
export const checkAndUpdateCourseCompletion = async (courseId: string, userId: string): Promise<boolean> => {
  try {
    console.log(`Checking course completion for course ${courseId} and user ${userId}`);

    if (!courseId || !userId) {
      console.error('Missing required parameters:', { courseId, userId });
      return false;
    }

    // Get all modules for this course
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id')
      .eq('course_id', courseId);

    if (modulesError) {
      console.error('Error fetching modules for course:', modulesError);
      return false;
    }

    if (!modules || modules.length === 0) {
      console.log(`No modules found for course ${courseId}`);
      return false;
    }

    console.log(`Found ${modules.length} modules for course ${courseId}`);

    // Get completed modules for this user
    const moduleIds = modules.map(m => m.id);

    const { data: userModuleProgress, error: progressError } = await supabase
      .from('user_module_progress')
      .select('*')
      .eq('user_id', userId)
      .in('module_id', moduleIds)
      .eq('is_completed', true);

    if (progressError) {
      console.error('Error fetching completed modules:', progressError);
      return false;
    }

    console.log(`User has completed ${userModuleProgress?.length || 0} out of ${modules.length} modules`);

    // If all modules are completed, mark the course as completed
    if (userModuleProgress && userModuleProgress.length === modules.length) {
      console.log(`All modules completed for course ${courseId}, marking course as completed for user ${userId}`);

      // Use the completeCourse function to mark the course as completed
      const { completeCourse } = await import('./completionService');
      const success = await completeCourse(courseId, userId);

      if (success) {
        console.log(`Successfully marked course ${courseId} as completed for user ${userId}`);

        // Award badge for completing the course
        try {
          console.log(`Awarding badge for completing course ${courseId}`);
          await awardCourseBadge(userId, courseId);
        } catch (badgeError) {
          console.error('Error awarding course badge:', badgeError);
          // Continue anyway - badge awarding should not block course completion
        }

        return true;
      } else {
        console.error(`Failed to mark course ${courseId} as completed for user ${userId}`);
        return false;
      }
    } else {
      console.log(`Not all modules completed for course ${courseId}`);
      return false;
    }
  } catch (error) {
    console.error('Unexpected error in checkAndUpdateCourseCompletion:', error);
    return false;
  }
};

// Track time spent on a course
export const trackCourseTime = async (courseId: string, userId: string, hoursSpent: number): Promise<boolean> => {
  try {
    // Check if there's already a progress record
    const { data: existingProgress } = await supabase
      .from('user_course_progress')
      .select('*')
      .eq('user_id', userId)
      .eq('course_id', courseId)
      .maybeSingle();

    if (existingProgress) {
      // Update existing record
      const { error } = await supabase
        .from('user_course_progress')
        .update({
          hours_spent: existingProgress.hours_spent + hoursSpent,
          last_accessed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', existingProgress.id);

      if (error) {
        console.error('Error updating course time:', error);
        return false;
      }
    } else {
      // Create new record
      const { error } = await supabase
        .from('user_course_progress')
        .insert([{
          user_id: userId,
          course_id: courseId,
          hours_spent: hoursSpent,
          last_accessed_at: new Date().toISOString()
        }]);

      if (error) {
        console.error('Error creating course progress:', error);
        return false;
      }
    }

    return true;
  } catch (error: any) {
    console.error('Unexpected error tracking course time:', error);
    return false;
  }
};

// Get user progress for a specific lesson
export const getLessonProgress = async (lessonId: string, userId: string): Promise<UserLessonProgress | null> => {
  if (!userId || !lessonId) return null;

  const { data, error } = await supabase
    .from('user_lesson_progress')
    .select('*')
    .eq('user_id', userId)
    .eq('lesson_id', lessonId)
    .maybeSingle();

  if (error) {
    console.error('Error fetching lesson progress:', error);
    return null;
  }

  // Ensure all required fields are present, with defaults if needed
  if (data) {
    return {
      ...data,
      // Explicitly cast data to include last_position with a default value if it doesn't exist
      last_position: 'last_position' in data ? data.last_position : 0
    } as UserLessonProgress;
  }

  return null;
};

// Get user progress for a specific course
export const getCourseProgress = async (courseId: string, userId: string): Promise<UserCourseProgress | null> => {
  if (!userId || !courseId) return null;

  const { data, error } = await supabase
    .from('user_course_progress')
    .select('*')
    .eq('user_id', userId)
    .eq('course_id', courseId)
    .maybeSingle();

  if (error) {
    console.error('Error fetching course progress:', error);
    return null;
  }

  return data;
};

// Calculate detailed course progress percentage based on completed lessons
export const calculateCourseProgress = async (courseId: string, userId: string): Promise<number> => {
  if (!userId || !courseId) return 0;

  try {
    console.log(`Calculating detailed course progress for course ${courseId} and user ${userId}`);

    // Step 1: Get all lessons for this course
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id')
      .eq('course_id', courseId);

    if (modulesError) {
      console.error('Error fetching modules:', modulesError);
      return 0;
    }

    if (!modules || modules.length === 0) {
      console.log(`No modules found for course ${courseId}`);
      return 0;
    }

    const moduleIds = modules.map(m => m.id);

    // Step 2: Get all lessons for these modules
    const { data: lessons, error: lessonsError } = await supabase
      .from('lessons')
      .select('id')
      .in('module_id', moduleIds);

    if (lessonsError) {
      console.error('Error fetching lessons:', lessonsError);
      return 0;
    }

    if (!lessons || lessons.length === 0) {
      console.log(`No lessons found for course ${courseId}`);
      return 0;
    }

    const lessonIds = lessons.map(l => l.id);
    const totalLessons = lessonIds.length;

    // Step 3: Get completed lessons for this user
    const { data: completedLessons, error: progressError } = await supabase
      .from('user_lesson_progress')
      .select('lesson_id')
      .eq('user_id', userId)
      .eq('is_completed', true)
      .in('lesson_id', lessonIds);

    if (progressError) {
      console.error('Error fetching completed lessons:', progressError);
      return 0;
    }

    // Step 4: Calculate progress percentage
    const completedCount = completedLessons?.length || 0;
    const progressPercentage = totalLessons > 0 ? (completedCount / totalLessons) * 100 : 0;

    console.log(`Course ${courseId} progress: ${completedCount}/${totalLessons} lessons completed (${progressPercentage.toFixed(1)}%)`);

    return progressPercentage;
  } catch (error) {
    console.error('Unexpected error calculating course progress:', error);
    return 0;
  }
};

// Save lesson position (for resuming later)
export const saveLessonPosition = async (lessonId: string, userId: string, position: number): Promise<boolean> => {
  try {
    // Check if there's already a progress record
    const { data: existingProgress } = await supabase
      .from('user_lesson_progress')
      .select('*')
      .eq('user_id', userId)
      .eq('lesson_id', lessonId)
      .maybeSingle();

    if (existingProgress) {
      // Update existing record
      const { error } = await supabase
        .from('user_lesson_progress')
        .update({
          last_position: position.toString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', existingProgress.id);

      if (error) {
        console.error('Error updating lesson position:', error);
        return false;
      }
    } else {
      // Create new record
      const { error } = await supabase
        .from('user_lesson_progress')
        .insert({
          user_id: userId,
          lesson_id: lessonId,
          last_position: position.toString(),
          is_completed: false
        });

      if (error) {
        console.error('Error creating lesson progress:', error);
        return false;
      }
    }

    return true;
  } catch (error: any) {
    console.error('Unexpected error saving lesson position:', error);
    return false;
  }
};

// Get user progress summary for all courses
export const getUserProgressSummary = async (userId: string): Promise<any> => {
  if (!userId) return null;

  try {
    // Get total completed lessons
    const { data: lessonProgress, error: lessonError } = await supabase
      .from('user_lesson_progress')
      .select('count')
      .eq('user_id', userId)
      .eq('is_completed', true);

    if (lessonError) {
      console.error('Error fetching lesson progress count:', lessonError);
      return null;
    }

    // Get total time spent across all courses
    const { data: courseProgress, error: courseError } = await supabase
      .from('user_course_progress')
      .select('hours_spent')
      .eq('user_id', userId);

    if (courseError) {
      console.error('Error fetching course progress:', courseError);
      return null;
    }

    // Calculate total hours spent
    const totalHoursSpent = courseProgress?.reduce((total, item) => total + (item.hours_spent || 0), 0) || 0;

    // Get completed courses count
    const { data: completedCourses, error: completedError } = await supabase
      .from('user_course_enrollment')
      .select('count')
      .eq('user_id', userId)
      .eq('status', 'completed');

    if (completedError) {
      console.error('Error fetching completed courses count:', completedError);
      return null;
    }

    return {
      completedLessons: lessonProgress?.[0]?.count || 0,
      totalHoursSpent,
      completedCourses: completedCourses?.[0]?.count || 0
    };
  } catch (error: any) {
    console.error('Unexpected error getting user progress summary:', error);
    return null;
  }
};
